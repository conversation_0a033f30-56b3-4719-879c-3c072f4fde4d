import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// No local imports needed - using web images

const GallerySection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState({});
  const dotsRef = useRef([]);

  const galleryImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1484&q=80',
      alt: 'Collaborative Workspace',
      caption: 'Team Collaboration Hub',
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Product Strategy Meeting',
      caption: 'Strategic Planning Session',
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Company Town Hall',
      caption: 'All-Hands Meeting',
    },
    {
      id: 4,
      src: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Brainstorming Session',
      caption: 'Innovation Workshop',
    },
    {
      id: 5,
      src: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Modern Office Interior',
      caption: 'Our Inspiring Workplace',
    },
    {
      id: 6,
      src: 'https://images.unsplash.com/photo-1581093450021-4a7360e9a6b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Technology Lab',
      caption: 'R&D and Innovation Lab',
    },
     {
      id: 7,
      src: 'https://images.unsplash.com/photo-1573496774439-fe217036005c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Team Recognition Event',
      caption: 'Celebrating Success',
    },
    {
      id: 8,
      src: 'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Client Presentation',
      caption: 'Client Partnership',
    },
     {
      id: 9,
      src: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
      alt: 'Team lunch event',
      caption: 'Company Culture',
    }
  ];

  // Group images into slides of 3
  const imagesPerSlide = 3;
  const slides = [];
  for (let i = 0; i < galleryImages.length; i += imagesPerSlide) {
    slides.push(galleryImages.slice(i, i + imagesPerSlide));
  }
  const totalSlides = slides.length;

  // Auto-advance carousel functionality
  useEffect(() => {
    if (!isPlaying || isHovered) return;

    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % totalSlides);
    }, 4000); // 4 seconds per slide

    return () => clearInterval(interval);
  }, [isPlaying, isHovered, totalSlides]);

  // Handle image loading
  const handleImageLoad = (imageId) => {
    setImageLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  // Navigation functions
  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // GSAP animations for dot indicators
  useEffect(() => {
    dotsRef.current.forEach((dot, index) => {
      if (dot) {
        gsap.to(dot, {
          scale: index === currentSlide ? 1.2 : 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  }, [currentSlide]);

  return (
    <section
      className="position-relative overflow-hidden"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)',
        display: 'flex',
        alignItems: 'center',
        padding: '80px 0'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background elements */}
      <div className="position-absolute w-100 h-100" style={{ background: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>')`, opacity: 0.3, zIndex: 0 }} />
      <div className="position-absolute w-100 h-100" style={{ zIndex: 1, pointerEvents: 'none' }}>{[...Array(6)].map((_, i) => (<div key={i} className="position-absolute" style={{ width: `${60 + Math.random() * 40}px`, height: `${60 + Math.random() * 40}px`, background: `linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3))`, borderRadius: Math.random() > 0.5 ? '50%' : '20%', top: `${Math.random() * 80 + 10}%`, left: `${Math.random() * 80 + 10}%`, backdropFilter: 'blur(10px)', border: '1px solid rgba(0, 160, 233, 0.2)', animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`, animationDelay: `${Math.random() * 2}s` }} />))}</div>

      <div className="container position-relative" style={{ zIndex: 2 }}>
        {/* Section Header */}
        <div className="text-center mb-5">
          <h2 className="display-4 fw-bold mb-4" style={{ background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', textShadow: '0 0 30px rgba(0, 160, 233, 0.3)' }}>
            Inside Our Office Walls
          </h2>
          <p className="lead mb-4" style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '1.2rem', maxWidth: '600px', margin: '0 auto' }}>
            A glimpse into our vibrant culture, collaborative spaces, and memorable moments.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="position-relative" style={{ height: '500px', overflow: 'hidden' }}>
          {/* Carousel Track */}
          <div
            className="d-flex h-100"
            style={{
              transform: `translateX(-${currentSlide * 100}%)`,
              transition: 'transform 1s cubic-bezier(0.76, 0, 0.24, 1)',
            }}
          >
            {/* Map over the grouped slides */}
            {slides.map((slide, slideIndex) => (
              <div
                key={slideIndex}
                className="w-100 h-100 flex-shrink-0 d-flex justify-content-center align-items-center"
                style={{ padding: '0 100px' }} // Provides space for nav buttons
              >
                {/* Inner container for the 3 images */}
                <div className="d-flex w-100 h-100" style={{ gap: '20px' }}>
                  {slide.map((image) => (
                    <div
                      key={image.id}
                      className="position-relative h-100"
                      style={{
                        flex: 1, // Each image container takes equal space
                        borderRadius: '16px',
                        overflow: 'hidden',
                        transition: 'transform 0.4s ease, box-shadow 0.4s ease',
                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-10px) scale(1.03)';
                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(0, 160, 233, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
                      }}
                    >
                      <img
                        src={image.src}
                        alt={image.alt}
                        className="w-100 h-100"
                        style={{ objectFit: 'cover' }}
                        onLoad={() => handleImageLoad(image.id)}
                      />
                      {/* Caption Overlay */}
                      <div className="position-absolute bottom-0 start-0 w-100 p-3"
                        style={{
                          background: 'linear-gradient(to top, rgba(0, 25, 55, 0.9) 0%, transparent 100%)',
                          color: 'white'
                        }}
                      >
                        <h6 className="fw-bold mb-0">{image.caption}</h6>
                      </div>
                      
                      {/* Loading State */}
                      {!imageLoaded[image.id] && (
                        <div className="position-absolute w-100 h-100 d-flex align-items-center justify-content-center" style={{ background: 'rgba(0, 41, 86, 0.9)', top: 0, left: 0, zIndex: 4 }}>
                          <div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="position-absolute top-50 start-0 translate-middle-y" style={{ zIndex: 6 }}>
            <button onClick={goToPrevious} className="btn nav-btn" aria-label="Previous images"><i className="fas fa-chevron-left"></i></button>
          </div>
          <div className="position-absolute top-50 end-0 translate-middle-y" style={{ zIndex: 6 }}>
            <button onClick={goToNext} className="btn nav-btn" aria-label="Next images"><i className="fas fa-chevron-right"></i></button>
          </div>
          <div className="position-absolute top-0 end-0 m-3" style={{ zIndex: 6 }}>
            <button onClick={togglePlayPause} className="btn nav-btn-play" aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}>
              <i className={`fas fa-${isPlaying ? 'pause' : 'play'}`}></i>
            </button>
          </div>
        </div>

        {/* Dot Indicators */}
        <div className="d-flex justify-content-center align-items-center mt-5">
          {slides.map((_, index) => (
            <button
              key={index}
              ref={el => dotsRef.current[index] = el}
              onClick={() => goToSlide(index)}
              className="btn p-0 mx-2 dot-indicator"
              style={{
                width: index === currentSlide ? '16px' : '12px',
                height: index === currentSlide ? '16px' : '12px',
                background: index === currentSlide ? 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)' : 'rgba(255, 255, 255, 0.4)',
                border: index === currentSlide ? '2px solid rgba(255, 255, 255, 0.5)' : '1px solid rgba(255, 255, 255, 0.3)',
                boxShadow: index === currentSlide ? '0 0 25px rgba(0, 160, 233, 0.6)' : '0 2px 8px rgba(0, 0, 0, 0.2)',
              }}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* CSS Animations and Styles */}
      <style>{`
        @keyframes float { 0%, 100% { transform: translateY(0px) rotate(0deg); } 33% { transform: translateY(-15px) rotate(120deg); } 66% { transform: translateY(5px) rotate(240deg); } }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        
        .nav-btn, .nav-btn-play {
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, rgba(0, 160, 233, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          color: white;
          font-size: 16px;
          transition: all 0.4s ease;
          box-shadow: 0 8px 25px rgba(0, 43, 89, 0.2);
        }
        .nav-btn:hover {
          background: linear-gradient(135deg, rgba(0, 160, 233, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
          transform: scale(1.1);
          box-shadow: 0 10px 30px rgba(0, 160, 233, 0.3);
        }
        .nav-btn-play {
          animation: ${!isPlaying ? 'pulse 2s ease-in-out infinite' : 'none'};
        }
        .dot-indicator {
          border-radius: 50%;
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
      `}</style>
    </section>
  );
};

export default GallerySection;