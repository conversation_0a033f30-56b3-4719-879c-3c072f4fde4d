import React, { useEffect, useRef, useState } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ServicesSection from '../components/ServicesSection';
import GallerySection from '../components/GallerySection';
import ClientsSection from '../components/ClientsSection';
import QuickTestingSection from '../components/QuickTestingSection';
import StunningLoadingScreen from '../components/StunningLoadingScreen';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const HomePage = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [loadingProgress, setLoadingProgress] = useState(0);

    // Refs for animation targets
    const heroRef = useRef(null);
    const titleRef = useRef(null);
    const subtitleRef = useRef(null);
    const ctaRef = useRef(null); // Ref for the stats section
    const visualizationRef = useRef(null); // Ref for the right-side visualization
    const loadingRef = useRef(null);
    
    // Refs for background elements (can be kept if needed for other effects)
    const particlesRef = useRef([]);
    const floatingElementsRef = useRef([]);

    // Loading animation effect
    useEffect(() => {
        const progressInterval = setInterval(() => {
            setLoadingProgress(prev => {
                if (prev >= 100) {
                    clearInterval(progressInterval);
                    if (loadingRef.current) {
                        gsap.to(loadingRef.current, {
                            opacity: 0,
                            scale: 0.8,
                            duration: 0.8,
                            ease: "power2.inOut",
                            onComplete: () => setIsLoading(false)
                        });
                    } else {
                        setIsLoading(false);
                    }
                    return 100;
                }
                return prev + Math.random() * 8 + 4;
            });
        }, 100);

        return () => clearInterval(progressInterval);
    }, []);

    // **UPDATED: Hero Section Animations from ATSDemo**
    useEffect(() => {
        // Run animations only after the loading is complete
        if (!isLoading) {
            const ctx = gsap.context(() => {
                const tl = gsap.timeline({ delay: 0.2 });

                // Title slides in from left with rotation
                if (titleRef.current) {
                    tl.from(titleRef.current, {
                        x: -200,
                        opacity: 0,
                        rotation: -10,
                        duration: 1.5,
                        ease: "power3.out"
                    });
                }

                // Subtitle slides in from right
                if (subtitleRef.current) {
                    tl.from(subtitleRef.current, {
                        x: 200,
                        opacity: 0,
                        duration: 1.2,
                        ease: "power2.out"
                    }, "-=1"); // Overlaps with title animation
                }

                // CTA (Stats Section) bounces in from bottom
                if (ctaRef.current) {
                    tl.from(ctaRef.current, {
                        y: 100,
                        opacity: 0,
                        scale: 0.8,
                        duration: 1,
                        ease: "bounce.out"
                    }, "-=0.8"); // Overlaps with subtitle animation
                }

                // Visualization rotates in
                if (visualizationRef.current) {
                    tl.from(visualizationRef.current, {
                        scale: 0,
                        rotation: 180,
                        opacity: 0,
                        duration: 1.5,
                        ease: "back.out(1.7)"
                    }, "-=1.2"); // Overlaps with CTA animation
                }

            }, heroRef);

            return () => ctx.revert();
        }
    }, [isLoading]);

    return (
        <>
            {/* Loading Screen */}
            {isLoading && (
                <StunningLoadingScreen ref={loadingRef} loadingProgress={loadingProgress} />
            )}

            {/* Stunning Hero Section */}
            <section
                ref={heroRef}
                className="position-relative overflow-hidden hero-section"
                style={{
                    minHeight: '100vh',
                    background: 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)',
                    display: 'flex',
                    alignItems: 'center'
                }}
            >
                {/* Background elements are kept the same for a consistent look */}
                <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
                    {[...Array(8)].map((_, i) => (
                        <div
                            key={i}
                            ref={el => floatingElementsRef.current[i] = el}
                            className="position-absolute"
                            style={{
                                width: `${60 + Math.random() * 40}px`, height: `${60 + Math.random() * 40}px`,
                                background: `linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3))`,
                                borderRadius: Math.random() > 0.5 ? '50%' : '20%',
                                top: `${Math.random() * 80 + 10}%`, left: `${Math.random() * 80 + 10}%`,
                                backdropFilter: 'blur(10px)', border: '1px solid rgba(0, 160, 233, 0.2)',
                                boxShadow: '0 8px 32px rgba(0, 160, 233, 0.1)',
                                animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
                                animationDelay: `${Math.random() * 2}s`
                            }}
                        />
                    ))}
                    {[...Array(20)].map((_, i) => (
                        <div
                            key={i}
                            ref={el => particlesRef.current[i] = el}
                            className="position-absolute"
                            style={{
                                width: '4px', height: '4px', background: '#00a0e9', borderRadius: '50%',
                                top: `${Math.random() * 100}%`, left: `${Math.random() * 100}%`,
                                opacity: 0.6, boxShadow: '0 0 10px #00a0e9'
                            }}
                        />
                    ))}
                </div>

                <Container className="position-relative" style={{ zIndex: 2 }}>
                    <Row className="align-items-center min-vh-100">
                        <Col lg={6} className="text-white">
                            {/* Title - ref is applied here */}
                            <div ref={titleRef} className="mb-4">
                                <h1
                                    className="display-1 fw-bold mb-4"
                                    style={{
                                        fontSize: 'clamp(3rem, 8vw, 4.4rem)', lineHeight: '1.1',
                                        background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                                        WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent',
                                        backgroundClip: 'text', textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
                                    }}
                                >
                                    Redefining.<br />
                                    <span style={{ color: '#00a0e9' }}>Quality.</span>
                                </h1>
                            </div>
                            
                            {/* Subtitle - ref is applied here */}
                            <div ref={subtitleRef}>
                                <p
                                    className="lead mb-5"
                                    style={{
                                        fontSize: '1.2rem', lineHeight: '1.6',
                                        color: 'rgba(255, 255, 255, 0.9)', maxWidth: '600px'
                                    }}
                                >
                                    Empowering businesses with cutting-edge technology solutions.
                                    From IoT to AI, we transform your vision into reality with
                                    innovative software that drives growth and success.
                                </p>
                            </div>

                            {/* Stats Section (CTA) - ref is applied to the Row */}
                            <Row ref={ctaRef} className="mt-5 pt-4">
                                {[
                                    { number: '180+', label: 'Employees' },
                                    { number: '99%', label: 'Client Satisfaction' },
                                    { number: '24/7', label: 'Support Available' }
                                ].map((stat, index) => (
                                    <Col xs={4} key={index} className="text-center">
                                        <div
                                            className="p-3 rounded-4"
                                            style={{
                                                background: 'rgba(255, 255, 255, 0.05)', backdropFilter: 'blur(10px)',
                                                border: '1px solid rgba(255, 255, 255, 0.1)'
                                            }}
                                        >
                                            <h3 className="fw-bold mb-1" style={{ color: '#00a0e9', fontSize: '1.8rem' }}>
                                                {stat.number}
                                            </h3>
                                            <p className="small mb-0" style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '0.9rem' }}>
                                                {stat.label}
                                            </p>
                                        </div>
                                    </Col>
                                ))}
                            </Row>
                        </Col>

                        <Col lg={6} className="d-none d-lg-block">
                            {/* 3D Tech Visualization - ref is applied here */}
                            <div
                                ref={visualizationRef}
                                className="position-relative"
                                style={{ height: '600px', perspective: '1000px' }}
                            >
                                {/* Central Tech Hub */}
                                <div
                                    className="position-absolute top-50 start-50 translate-middle"
                                    style={{
                                        width: '200px', height: '200px',
                                        background: 'linear-gradient(135deg, rgba(0, 160, 233, 0.2), rgba(0, 160, 233, 0.4))',
                                        borderRadius: '50%', backdropFilter: 'blur(20px)',
                                        border: '2px solid rgba(0, 160, 233, 0.3)',
                                        boxShadow: '0 0 60px rgba(0, 160, 233, 0.4)',
                                        display: 'flex', alignItems: 'center', justifyContent: 'center',
                                        animation: 'pulse 3s ease-in-out infinite'
                                    }}
                                >
                                    <lord-icon
                                        src="https://cdn.lordicon.com/qhviklyi.json" trigger="hover"
                                        colors="primary:#00a0e9"
                                        style={{ width: '80px', height: '80px', filter: 'drop-shadow(0 0 20px rgba(0, 160, 233, 0.8))' }}>
                                    </lord-icon>
                                </div>
                                {/* Orbiting icons and connection lines remain the same */}
                                {[
                                    { src:{Makonis-Logo.png}, angle: 0, radius: 150, color: '#00a0e9' },
                                    { lordicon: 'https://cdn.lordicon.com/hwjcdycb.json', angle: 60, radius: 180, color: '#0056b3' },
                                    { lordicon: 'https://cdn.lordicon.com/dxjqoygy.json', angle: 120, radius: 160, color: '#00a0e9' },
                                    { lordicon: 'https://cdn.lordicon.com/ipnwkgdy.json', angle: 180, radius: 170, color: '#0056b3' },
                                    { lordicon: 'https://cdn.lordicon.com/wloilxuq.json', angle: 240, radius: 155, color: '#00a0e9' },
                                    { lordicon: 'https://cdn.lordicon.com/qhgmphtg.json', angle: 300, radius: 175, color: '#0056b3' }
                                ].map((item, index) => (
                                    <div
                                        key={index}
                                        className="position-absolute"
                                        style={{
                                            top: '50%', left: '50%',
                                            transform: `translate(-50%, -50%) rotate(${item.angle}deg) translateX(${item.radius}px) rotate(-${item.angle}deg)`,
                                            width: '80px', height: '80px',
                                            background: `linear-gradient(135deg, ${item.color}20, ${item.color}40)`,
                                            borderRadius: '20px', backdropFilter: 'blur(10px)', border: `1px solid ${item.color}30`,
                                            display: 'flex', alignItems: 'center', justifyContent: 'center',
                                            boxShadow: `0 8px 32px ${item.color}20`,
                                            animation: `orbit 20s linear infinite`,
                                            animationDelay: `${index * -3.33}s`,
                                            '--radius': `${item.radius}px`
                                        }}
                                    >
                                        <lord-icon
                                            src={item.lordicon} trigger="hover" colors={`primary:${item.color}`}
                                            style={{ width: '24px', height: '24px' }}>
                                        </lord-icon>
                                    </div>
                                ))}
                            </div>
                        </Col>
                    </Row>
                </Container>

                <style>{`
                    /* All necessary CSS keyframes from both files are included here for completeness */
                    @keyframes float { 
                        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
                        33% { transform: translateY(-15px) rotate(120deg); opacity: 1; }
                        66% { transform: translateY(5px) rotate(240deg); opacity: 0.8; }
                    }
                    @keyframes pulse {
                        0% { transform: translate(-50%, -50%) scale(1); box-shadow: 0 0 40px rgba(0, 160, 233, 0.4); }
                        50% { transform: translate(-50%, -50%) scale(1.05); box-shadow: 0 0 70px rgba(0, 160, 233, 0.6); }
                        100% { transform: translate(-50%, -50%) scale(1); box-shadow: 0 0 40px rgba(0, 160, 233, 0.4); }
                    }
                    @keyframes orbit {
                        from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
                        to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
                    }
                    /* Add any other required keyframes here */
                `}</style>
            </section>

            {/* Other sections of your page */}
            <ServicesSection />
            <GallerySection />
            <ClientsSection />
            <QuickTestingSection />
        </>
    );
};

export default HomePage;